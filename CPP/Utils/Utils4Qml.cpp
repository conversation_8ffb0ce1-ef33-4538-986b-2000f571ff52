﻿#include "Utils4Qml.h"

std::unique_ptr<Utils4Qml> Utils4Qml::instance_;
std::mutex                 Utils4Qml::mutex_;


Utils4Qml *Utils4Qml::getInstance() {
    std::lock_guard<std::mutex> lock(mutex_);

    if (instance_ == nullptr) {
        instance_.reset(new Utils4Qml());
    }
    return instance_.get();
}

bool Utils4Qml::isGoodsBarcode(QVariant in_str)
{
    QString str_in = in_str.toString();

    bool is_numeric = false;
    str_in.toULongLong(&is_numeric);

    if (!is_numeric || str_in.length() < 8)
        return false;

    //缩短码
    {
        if (str_in.length() == 8)
        {
            return true;
        }
    }

    //无码?
    {
        if (str_in.length() == 12)
        {
            return true;
        }
    }

    //中国
    {
        std::vector<QString> prefix_vec;
        prefix_vec.push_back("69");
        if (Utils::String::isHavePrefixs(str_in, prefix_vec))
        {
            if (str_in.length() == 13)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
    }


    //日本
    {
        std::vector<QString> prefix_vec;

        for (int i = 45; i <= 49; ++i)
        {
            prefix_vec.push_back(QString::number(i));
        }

        if (Utils::String::isHavePrefixs(str_in, prefix_vec))
        {
            if (str_in.length() == 13)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
    }

    //英国
    {
        std::vector<QString> prefix_vec;

        for (int i = 50; i <= 59; ++i)
        {
            prefix_vec.push_back(QString::number(i));
        }

        if (Utils::String::isHavePrefixs(str_in, prefix_vec))
        {
            if (str_in.length() == 13)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
    }

    //德国
    {
        std::vector<QString> prefix_vec;

        for (int i = 400; i <= 440; ++i)
        {
            prefix_vec.push_back(QString::number(i));
        }

        if (Utils::String::isHavePrefixs(str_in, prefix_vec))
        {
            if (str_in.length() == 13)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
    }

    //美国
    {
        if (str_in.length() == 12)
        {
            return true;
        }
    }

    //欧洲
    {
        std::vector<QString> prefix_vec;

        for (int i = 0; i <= 19; ++i)
        {
            std::stringstream string_stream;
            string_stream << std::setfill('0') << std::setw(2) << i;
            prefix_vec.push_back(QString::fromStdString(string_stream.str()));
        }

        if (Utils::String::isHavePrefixs(str_in, prefix_vec))
        {
            if (str_in.length() == 13)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
    }

    {
        std::vector<QString> prefix_vec;

        auto barcode_weight_prefix = ConfigTool::getInstance()->getSetting(ConfigEnum::BAR_CODE_WEIGHING_PRE_TWO_CODE).toString();
        prefix_vec.push_back(barcode_weight_prefix);

        if (Utils::String::isHavePrefixs(in_str.toString(), prefix_vec))
        {
            if (str_in.length() == 13)
            {
                return true;
            }
        }
    }

    return false;
}

bool Utils4Qml::isDaHuaNocodeGoodsBarcode(QString str_in)
{
    bool is_numeric = false;
    str_in.toInt(&is_numeric);

    if (!is_numeric || str_in.length() < 8)
        return false;

    //无码?
    {
        if (str_in.length() == 12)
        {
            return true;
        }
    }
    return false;
}

Utils4Qml::Utils4Qml(QObject *parent) : QObject{parent}
{}
